# CrystalPool System

## Overview

The CrystalPool system is an optimization for managing Crystal objects in the space shooter game. Instead of constantly creating and destroying crystal objects (which causes garbage collection pressure and performance issues), the pool system reuses crystal objects.

## How It Works

### ObjectPool (Base Class)
- Generic object pool implementation that can be extended for any object type
- Manages a pool of pre-created objects for reuse
- Handles object lifecycle: creation, retrieval, return, and cleanup
- Configurable initial and maximum pool sizes

### CrystalPool (Specialized Class)
- Extends ObjectPool specifically for Crystal objects
- Provides specialized reset logic for crystal properties
- Offers convenient `get_crystal()` and `return_crystal()` methods
- Handles crystal initialization after retrieval from pool

### Crystal Classes (Modified)
- **ChristalRigid.gd**: Added `crystal_pool` reference, `set_crystal_pool()` method, `reset_for_pool()` method, and modified `destroy()` method to return crystals to pool instead of `queue_free()`
- All crystal instances now have `crystal_pool` property to track which pool they belong to

## Usage

### Initialization (in Game.gd)
```gdscript
# Initialize crystal pool for optimized crystal management
crystal_pool = CrystalPool.create_crystal_pool(self)  # 50 initial, 300 max
```

### Getting Crystals (replaces Crystal.instance())
```gdscript
# Old way:
var crystal = Crystal.instance()
crystal.init(crystalType, doSpreadMore)
add_child(crystal)

# New way:
var crystal = crystal_pool.get_crystal()
if crystal:
    crystal.init(crystalType, doSpreadMore)
    # Note: crystal is automatically added to scene tree by pool
```

### Returning Crystals (automatic)
```gdscript
# Crystals are automatically returned to pool when destroyed
crystal.destroy()  # Returns to pool instead of queue_free()

# Or when going off-screen (handled internally)
# Or when collected by player (handled internally)
```

## Performance Benefits

1. **Reduced Garbage Collection**: Crystals are reused instead of created/destroyed
2. **Faster Object Creation**: Pre-instantiated crystals ready for immediate use
3. **Memory Efficiency**: Controlled memory usage with configurable pool limits
4. **Consistent Performance**: Eliminates GC spikes during intense crystal spawning scenarios

## Configuration

The crystal pool is configured with:
- **Initial Size**: 50 crystals (pre-created at startup)
- **Maximum Size**: 300 crystals (pool won't grow beyond this)

These values can be adjusted in `CrystalPool.create_crystal_pool()` call.

## Files Modified

1. **New Files**:
   - `scripts/CrystalPool.gd` - Specialized crystal pool class

2. **Modified Files**:
   - `scripts/ChristalRigid.gd` - Added pool support methods and modified destroy behavior
   - `scripts/Game.gd` - Updated to use CrystalPool instead of direct instantiation

## Testing

The system includes test scripts:
- `scripts/CrystalPoolTest.gd` - Comprehensive pool functionality test

## Technical Details

### Crystal Reset Logic
When crystals are returned to the pool, they are reset to default state:
- Crystal type and value reset to defaults
- Physics variables (velocity, gravity) reset
- Visual state (position, rotation, scale, modulation) reset
- Animation state reset
- Cache variables cleared

### Pool Statistics
The pool provides statistics for monitoring:
- Available objects in pool
- Total objects created
- Objects currently in use
- Pool efficiency metrics

## Integration Notes

- The crystal pool integrates seamlessly with existing crystal spawning logic
- All crystal types (c5, c10, c20, c50, c100, c200) are supported
- Magnet effects and physics simulation work unchanged
- Crystal collection and value calculation remain the same

## Future Enhancements

Potential improvements:
- Separate pools for different crystal values if needed
- Pool warming strategies for anticipated crystal spawns
- Dynamic pool size adjustment based on gameplay intensity
- Pool statistics monitoring and optimization
