# Object Pool System

## Overview

The Object Pool system is an optimization for managing frequently created/destroyed objects in the space shooter game. Instead of constantly creating and destroying objects (which causes garbage collection pressure and performance issues), the pool system reuses objects.

Currently implemented pools:
- **BulletPool**: For all bullet types (regular, super, homing, laser bullets)
- **CrystalPool**: For crystal objects

## BulletPool System

The BulletPool system manages all bullet types: regular bullets, super bullets, homing bullets, and laser bullets.

## Architecture

### ObjectPool (Base Class)
- Generic object pooling system that can be used for any type of object
- Manages available and active object arrays
- Handles object lifecycle (get/return)
- Configurable pool sizes (initial and maximum)

### BulletPool (Specialized Class)
- Extends ObjectPool for all bullet types (Bullet, BulletSuper, BulletHoming, BulletLaser)
- Provides specialized reset logic for different bullet properties
- Offers convenient `get_bullet()` and `return_bullet()` methods
- Handles bullet initialization after retrieval from pool
- Supports multiple pool types with factory methods

### Bullet Classes (Modified)
- **BulletBase.gd**: Added `reset_for_pool()` method and `set_bullet_pool()` method, modified `destroy()` method to return bullets to pool
- **BulletHoming.gd**: Already had pool support, now uses dedicated homing pool
- **BulletLaser.gd**: Added pool support with laser-specific reset logic
- **BulletSuper.gd**: Inherits pool support from BulletBase
- All bullet classes now have `bullet_pool` property to track which pool they belong to

## Usage

### Initialization (in Game.gd)
```gdscript
# Initialize bullet pools for optimized bullet management
bullet_pool = BulletPool.create_bullet_pool(self)           # For regular bullets
bullet_super_pool = BulletPool.create_bullet_super_pool(self) # For super bullets
bullet_homing_pool = BulletPool.create_bullet_homing_pool(self) # For homing bullets
bullet_laser_pool = BulletPool.create_bullet_laser_pool(self)   # For laser bullets
```

### Getting Bullets (replaces .instance() calls)
```gdscript
# Old way:
var bullet = Bullet.instance()
add_child(bullet)

# New way:
var bullet = bullet_pool.get_bullet()
bullet.position = startPosition
# Note: bullet is automatically added to scene tree by pool

# For different bullet types:
var superBullet = bullet_super_pool.get_bullet()
var homingBullet = bullet_homing_pool.get_bullet()
var laserBullet = bullet_laser_pool.get_bullet()
```

### Returning Bullets (automatic)
Bullets are automatically returned to the pool when their `destroy()` method is called, which happens when:
- Bullet hits an enemy
- Bullet goes off-screen
- Bullet lifetime expires

## Performance Benefits

1. **Reduced Garbage Collection**: Reusing objects instead of creating/destroying them
2. **Faster Object Creation**: Pool objects are pre-instantiated and ready to use
3. **Memory Efficiency**: Controlled memory usage with configurable pool limits
4. **Consistent Performance**: Eliminates GC spikes during intense bullet spawning

## Configuration

The pools are configured with different sizes based on usage patterns:
- **Regular Bullets**: Initial 30, Max 200 (most frequently used)
- **Super Bullets**: Initial 20, Max 150 (moderately used)
- **Homing Bullets**: Initial 20, Max 150 (moderately used)
- **Laser Bullets**: Initial 10, Max 50 (less frequently used)

These values can be adjusted in the respective `BulletPool.create_*_pool()` factory methods.

## Files Modified

1. **New Files**:
   - `scripts/ObjectPool.gd` - Generic object pool base class
   - `scripts/BulletPool.gd` - Generic bullet pool class supporting all bullet types

2. **Modified Files**:
   - `scripts/BulletBase.gd` - Added pool support methods and modified destroy behavior
   - `scripts/BulletHoming.gd` - Already had pool support, now uses dedicated homing pool
   - `scripts/BulletLaser.gd` - Added pool support with laser-specific reset logic
   - `scripts/BulletSuper.gd` - Inherits pool support from BulletBase
   - `scripts/Game.gd` - Updated to use multiple BulletPools instead of direct instantiation

## Testing

The system includes test scripts:
- `scripts/BulletPoolTest.gd` - Comprehensive pool functionality test
- `scripts/PoolValidation.gd` - Basic validation of class creation

## Compatibility

This system is designed for Godot 3.6 and maintains full backward compatibility with existing bullet behavior while providing performance improvements.

### Godot 3.6 Specific Implementation Notes

- Uses composition instead of inheritance for BulletPool (to avoid cyclic reference issues)
- Removes type hints for compatibility with Godot 3.6 syntax
- Uses `load()` instead of direct class references in static methods
- All `export` variables and Godot 3.6 syntax preserved

## Troubleshooting

If you encounter "cyclic reference" errors:
1. Make sure you're using Godot 3.6, not Godot 4.x
2. Verify that class names are properly registered in project.godot
3. Check that no class references itself directly in static methods

## Performance Testing

To test the performance improvements:
1. Enable the bullet hell mode in game
2. Monitor frame rate during intense bullet spawning
3. Compare memory usage before/after implementation
4. Use the built-in pool statistics: `bullet_pool.get_stats()`
