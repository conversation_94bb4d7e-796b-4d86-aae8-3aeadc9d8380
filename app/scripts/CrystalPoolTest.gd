# Test script to validate CrystalPool functionality
# This script tests the pooling system for crystals

extends Node

# Test crystal pool functionality
func test_crystal_pool():
	print("=== CrystalPool Test Started ===")
	
	# Create crystal pool
	print("\n--- Creating Crystal Pool ---")
	var pool = CrystalPool.create_crystal_pool(self, 10, 50)
	print("Crystal pool created with initial size 10, max size 50")
	print("Initial stats: ", pool.get_stats())
	
	# Test getting crystals from pool
	print("\n--- Testing Crystal Retrieval ---")
	var crystals = []
	for i in range(8):
		var crystal = pool.get_crystal()
		if crystal:
			crystals.append(crystal)
			print("Got crystal ", i, " - Pool stats: ", pool.get_stats())
			
			# Initialize crystal with different types
			var crystal_type = i % len(Global.CrystalType)
			crystal.init(crystal_type, false)
			print("  Initialized crystal with type: ", crystal_type, ", value: ", crystal.crystalValue)
		else:
			print("Failed to get crystal ", i)
	
	print("Retrieved 8 crystals")
	print("Current stats: ", pool.get_stats())
	
	# Test returning crystals to pool
	print("\n--- Testing Crystal Return ---")
	for i in range(5):
		if i < crystals.size():
			pool.return_crystal(crystals[i])
			print("Returned crystal ", i, " - Pool stats: ", pool.get_stats())
	
	print("Returned 5 crystals")
	print("Final stats: ", pool.get_stats())
	
	# Test getting crystals again (should reuse returned ones)
	print("\n--- Testing Crystal Reuse ---")
	for i in range(3):
		var crystal = pool.get_crystal()
		if crystal:
			print("Reused crystal ", i, " - Pool stats: ", pool.get_stats())
			# Check if crystal was properly reset
			print("  Crystal reset state - wasInit: ", crystal.wasInit, ", visible: ", crystal.visible)
	
	print("\n=== CrystalPool Test Completed ===")

# Test crystal pool performance
func test_crystal_pool_performance():
	print("\n=== CrystalPool Performance Test Started ===")
	
	var pool = CrystalPool.create_crystal_pool(self, 20, 100)
	
	# Test rapid crystal creation and destruction
	var start_time = OS.get_ticks_msec()
	var crystals = []
	
	# Create many crystals
	for i in range(50):
		var crystal = pool.get_crystal()
		if crystal:
			crystal.init(Global.CrystalType.c5, false)
			crystals.append(crystal)
	
	# Return all crystals
	for crystal in crystals:
		pool.return_crystal(crystal)
	
	var end_time = OS.get_ticks_msec()
	var duration = end_time - start_time
	
	print("Created and returned 50 crystals in ", duration, " ms")
	print("Final pool stats: ", pool.get_stats())
	
	print("=== CrystalPool Performance Test Completed ===")

func _ready():
	# Run the tests
	call_deferred("test_crystal_pool")
	call_deferred("test_crystal_pool_performance")
