# CrystalPool - Specialized object pool for Crystal objects
# Extends ObjectPool to provide crystal-specific functionality

extends "res://scripts/ObjectPool.gd"

# Crystal scene to instantiate
var Crystal = preload("res://scenes/CrystalRigid.tscn")

# Factory method to create a crystal pool
static func create_crystal_pool(parent_node, initial_size = 50, max_size = 300):
	var pool = load("res://scripts/CrystalPool.gd").new()
	pool.parent_node = parent_node
	pool.initial_size = initial_size
	pool.max_size = max_size
	pool.initialize()
	return pool

# Override _create_object to create Crystal instances
func _create_object():
	var crystal = Crystal.instance()
	crystal.set_crystal_pool(self)
	return crystal

# Override _reset_object to reset crystal state
func _reset_object(crystal):
	if crystal and is_instance_valid(crystal):
		_reset_crystal_base(crystal)

# Reset crystal to default state for reuse
func _reset_crystal_base(crystal):
	# Reset crystal properties
	crystal.crystalType = Global.CrystalType.c5
	crystal.crystalValue = 5
	crystal.wasInit = false
	
	# Reset physics variables
	crystal.velocity = Vector2.ZERO
	crystal.gravity = 100.0
	crystal.initial_scatter_applied = false
	crystal.scatter_impulses_remaining = 1
	
	# Reset cached values
	crystal.cached_player_position = Vector2.ZERO
	crystal.cached_permanent_crystal_magnet = false
	crystal.cached_crystal_value_multiplier = 1.0
	crystal.player_position_update_counter = 0
	crystal.magnet_check_counter = 0
	crystal.cached_has_magnet_effect = false
	crystal.screen_check_counter = 0
	
	# Reset magnet settings to defaults
	crystal.magnet_power = 150.0
	crystal.magnet_range = 400.0
	
	# Reset visual state
	crystal.visible = false
	crystal.position = Vector2.ZERO
	crystal.global_position = Vector2.ZERO
	crystal.rotation_degrees = 0
	crystal.scale = Vector2(1, 1)
	crystal.modulate = Color(1, 1, 1, 1)
	
	# Reset animated sprite if it exists
	if crystal.has_node("AnimatedSprite"):
		crystal.get_node("AnimatedSprite").modulate = Color(1, 1, 1, 1)
		crystal.get_node("AnimatedSprite").animation = "c5"
		crystal.get_node("AnimatedSprite").frame = 0

# Get a crystal from the pool
func get_crystal():
	var crystal = get_object()
	if crystal:
		# Crystal will be added to scene tree by the pool
		return crystal
	return null

# Return a crystal to the pool
func return_crystal(crystal):
	return_object(crystal)
