extends Node

var SmallDebris = preload("res://scenes/SmallDebris.tscn")
var LargeDebris = preload("res://scenes/LargeDebris.tscn")

#map resources to enemy objects
var enemyResources = {
	"1": preload("res://scenes/Enemy_1.tscn"),
	"2": preload("res://scenes/Enemy_2.tscn"),
	"3": preload("res://scenes/Enemy_3.tscn"),
	"4": preload("res://scenes/Enemy_4.tscn"),
	"5": preload("res://scenes/Enemy_5.tscn"),
	"6": preload("res://scenes/Enemy_6.tscn"),
	"7": preload("res://scenes/Enemy_7.tscn"),
	"8": preload("res://scenes/Enemy_8.tscn"),
	"9": preload("res://scenes/Enemy_9.tscn"),
	"10": preload("res://scenes/Enemy_10.tscn"),
}

var enemySize = {
	"1": 1,
	"2": 1,
	"3": 1,
	"4": 1,
	"5": 1,
	"6": 1,
	"7": 3,
	"8": 1,
	"9": 1,
	"10": 1
}

var AreaExplosionCount = 0

func incAreaExplosionCnt():
	AreaExplosionCount+=1
	if(AreaExplosionCount>=4):
		Achievments.acquire("chain_reaction")

func decAreaExplosionCnt():
	AreaExplosionCount-=1
	AreaExplosionCount=max(0,AreaExplosionCount)

var wigglePosition = 0.0
var wiggleDirection = 1.0

var lives = max(Profile.getStat("lives", Config.ShipLifeBase), ShipSpecs.getSpecs().initial_live_count)
var score = 0
var money = 0
var shieldCount = max(Profile.getStat("shield", 0), ShipSpecs.getSpecs().initial_shield_count)

var Explosion = preload("res://scenes/Explosion_1.tscn")
var ObjectIndicator = preload("res://scenes/ObjectIndicator.tscn")

var levelConductor = null

var difficulty = Global.GameDifficulty.NORMAL

var BulletsOnScreen = 0
var PlayerSpeed = Profile.getStat("speed", Config.ShipSpeedBase)
var PlayerMaxBullets = Profile.getStat("bullets", Config.MaxBulletsBase)
var PlayerAutoFire = Profile.getStat("rapidfire", false)
var PlayerLuck = Config.StartLuckValue # luck multiplier for powerups and other bonuses the higher the more frequent bonuses are
var PlayerBulletType = Profile.getStat("weapon", Global.PlayerBulletTypes.SINGLE)

func getPlayerMaxBullets():
	var freq_mod = Global.getBulletData(PlayerBulletType)["freq_mod"];
	return floor(PlayerMaxBullets * ShipSpecs.getSpecs().bullet_multiplier*freq_mod)

var Powerup = preload("res://scenes/Powerup.tscn")
var BonusMultiplierLabel = preload("res://scenes/BonusMultiplierLabel.tscn")
var Crystal = preload("res://scenes/CrystalRigid.tscn")
var PowerupShip = preload("res://scenes/EnemyPowerupShip.tscn")
var Bullet = preload("res://scenes/Bullet.tscn")
var BulletSuper = preload("res://scenes/BulletSuper.tscn")
var BulletHoming = preload("res://scenes/BulletHoming.tscn")
var BulletLaser = preload("res://scenes/BulletLaser.tscn")
var LargeCrystal = preload("res://scenes/LargeCrystal.tscn")

# Bullet pools for optimized bullet management
var bullet_pool          # For regular bullets
var bullet_super_pool    # For super bullets
var bullet_homing_pool   # For homing bullets
var bullet_laser_pool    # For laser bullets

var ControlMode = Global.GameControlMode.KEYS

var isBulletOP = false

var _secretModuleContainer = ""

func getModeStr():
	if(isMode(Global.GameMode.FLOW)):
		return "flow"
	else:
		return "campaign"

func getEnemyEntrySpeed():
	if(isMode(Global.GameMode.FLOW)):
		return Config.EnemyEntrySpeed*0.8

	if(isMode(Global.GameMode.CAMPAIGN)):
		return Config.EnemyEntrySpeed*1.2 if difficulty==Global.GameDifficulty.EASY else Config.EnemyEntrySpeed*0.9

	return Config.EnemyEntrySpeed

func isMode(mode):
	return Global.OptionsData.gameMode == mode

func spawnLargeCrystal(isLarge = false):
	var crystal = LargeCrystal.instance()
	crystal.init(isLarge)
	Global.GameScene.add_child(crystal);

	Global.GameScene.addIndicator(crystal.global_position.x, Global.OISize.Small if !isLarge else Global.OISize.Large ,Global.OIType.Animated if isLarge else Global.OIType.Green,"???" if isLarge else "Loot",10 if isLarge else 3)

	return crystal

func setSpeed(isFast=false):
	$BG/BGStarsSlow.speed_scale = Global.ifelse(isFast,3.2,1.0)
	$BG/BGStars.speed_scale = Global.ifelse(isFast,3.2,1.0)
	$BG/BGStarsFast.speed_scale = Global.ifelse(isFast,3.2,1.0)

func addShield():

	if(shieldCount>=Config.MaxShields):
		Global.GameScene.spawnBonusLabel(player.global_position-Vector2(0,64) ,"Maxed",1.5,true,false,0.8);
		addScore(Config.ShipBulletPointsMaxShields)
		return false

	shieldCount+=1

func _customExplode(position, color):
	# play explosion
	var explosion = Explosion.instance()
	explosion.position = position
	explosion.z_index  = Config.TopZIndex+1000;
	explosion.explosionColorMod = color
	add_child(explosion);

func spawnExplosion(position, delay, color):
	Global.setTimeout(self, delay,self,"_customExplode",[position, color])

func isPowerShipOnScreen():
	return powerupShipCount>0

func isLargeCrystalOnScreen():
	return largeCrystalCount>0

var powerupShipCount = 0
var largeCrystalCount = 0

func spawnPowerupShip():
	var ship = PowerupShip.instance()
	add_child(ship)
	ship.call_deferred("init")

	Global.GameScene.addIndicator(ship.global_position.x, Global.OISize.Medium, Global.OIType.Yellow ,"Ship")

func addUpgradeModule(type):
	self._secretModuleContainer += type[0].to_lower()

	if(self._secretModuleContainer.length()==3):
		applyUpgradeModule(self._secretModuleContainer)
		self._secretModuleContainer = ""
	
var cursedComboCnt = 0

func applyUpgradeModule(moduleId):

	var appliedTxt = "";
	var wasCursedCombo = true

	match moduleId:

		"abc":
			if player:
				PlayerBulletType = Global.PlayerBulletTypes.STRONG_SINGLE

				for _i in range(10):
					player.incBullets()

				for _i in range(10):
					player.incSpeed()

				for _i in range(Config.MaxWingCount):
					player.addWings(Global.PlayerBulletTypes.HOMING_SINGLE);

			appliedTxt = "Weapon & Speed Boost!"

		"cba":
			PlayerLuck += 3.0
			appliedTxt = "Luck Boost!"

		"aaa":
			playerEffets[Global.PlayerEffect.SUPER_RAPIDFIRE] = 120
			appliedTxt = "RapidFire ++"

		"bbb":
			playerEffets[Global.PlayerEffect.INVINCIBILITY] = 120
			appliedTxt = "Invincibility ++"

		"bba":
			player.destroyWings(0)

			for _i in range(Global.getMaxWingCount()):
				player.addWings(Global.PlayerBulletTypes.HOMING_SINGLE);

			appliedTxt = "Full Wings"

		"ccc":
			playerEffets[Global.PlayerEffect.LUCK] = 120
			appliedTxt = "Luck ++"

		"cac":
			money = money * 5
			appliedTxt = "Money x 5"

		"bab":
			unlockNextShip()
			appliedTxt = "Ship unlock"

		# todo figure stuff out

		_:
			wasCursedCombo = false
			Achievments.acquire("cursed_trio_fail");
			Global.GameScene.displayNotification("Nothing to see here","NO SECRET")
			# default?
			pass

	if(wasCursedCombo):
		Achievments.acquire("curse_burst");
		Global.GameScene.spawnBonusLabel(Global.getWindowSize()/Vector2(2.0,2.0), "SECRET FOUND!",0.3, false, true, 2.0);
		Global.GameScene.displayNotification(appliedTxt,"CURSED SECRET")
		Global.playTts(SoundManager.tts_secret_found)

		if(cursedComboCnt>=1):
			Achievments.acquire("multiple_cursed_combos")

		cursedComboCnt+=1

var playerEffets = {
	Global.PlayerEffect.SUPER_RAPIDFIRE: 0,
	Global.PlayerEffect.INVINCIBILITY: 0,
	Global.PlayerEffect.CRYSTAL_EXPLOSION: 0,
	Global.PlayerEffect.WEAPON_DISABLED: 0,
	Global.PlayerEffect.LUCK: 0,
	Global.PlayerEffect.SLOTH_MODE: 0,
	Global.PlayerEffect.CRYSTAL_MAGNET: 0
}

var _type_storage = ""

func _input(ev):

	if(ev is InputEventKey && ev.pressed == false):

		var chr = ev.as_text()

		if(chr.length()==1):
			self._type_storage += chr

			var ln = self._type_storage.length()

			if(ln>5):
				self._type_storage = self._type_storage.substr(ln-5, -1)

			if(Config.CheatModeEnabled):
				self.checkCheat(self._type_storage)

func unlockNextShip():
	if(ShipSpecs.wasAllUnlocaked()):
		return false
	
	var _new_index = Profile.getNextShipIndex()
	var _new_ship = ShipSpecs.getPresets()[_new_index]

	if(_new_ship.min_difficulty<=self.getProperDifficulty()):
		var _temp = Profile.unlockNextShip()
		spawnBonusLabel(Global.getWindowSize()/Vector2(2.0,1.3), "NEW SHIP UNLOCKED!",0.3, false, true, 2);
		Global.playTts(SoundManager.tts_yaaaaaaaaa_hooooooooo)
		addTerminalLine("SHIP UNLOCKED: "+ _new_ship.name)
	else:
		addTerminalLine("INCREASE DIFFICULTY TO UNLOCK: " + _new_ship.name)



	if(ShipSpecs.wasAllUnlocaked()):
		Achievments.acquire("fleet_master")

var currentBg = 1

func changeBg(num):
	if(num==currentBg):
		return false
	if(num && (num>0 and num<5)):
		currentBg = num
		get_parent().get_node("GameBackground").changeImage(num)

func checkCheat(cheat, doCountAsCheat=true):
	cheat = cheat.to_lower()

	var wasMatch = true
	var wasCustomMatch = false
	var regex = RegEx.new()

	# cheat for level skip

	regex.compile("^lvl([0-9]{2})$")
	var regexresult = regex.search(cheat)

	if(regexresult!=null && Config.Env.IsDevelopment):
		var num = regexresult.get_string(1)
		num = int(num)
		if(num>1):
			levelConductor.currentLevel=num-1
			levelConductor.levelInstance.killEverything()
			levelConductor._initLevelLoad()

	# cheat for spawning powerup

	regex.compile("^pwu([0-9]{2})$")
	regexresult = regex.search(cheat)

	if(regexresult!=null):
		var num = regexresult.get_string(1)
		spawnPowerup( Vector2(Global.getWindowSize().x/2,-100) , int(num))
		wasCustomMatch = true

	# cheat for changing ship

	regex.compile("^chsh([0-9]{1})$")
	regexresult = regex.search(cheat)

	if(regexresult!=null && Config.Env.IsDevelopment):
		var num = int(regexresult.get_string(1))
		Global.getPlayer().applySpecs(num)
		wasCustomMatch = true

	# cheat for changing ship

	regex.compile("^chbg([0-9]{1})$")
	regexresult = regex.search(cheat)

	if(regexresult!=null):
		var num = int(regexresult.get_string(1))
		if(num>0 and num<5):
			self.changeBg(num)
	
	
	# if no custom cheat, then we see predefined stuff

	if (!wasCustomMatch):
		match cheat:

			"shake":
				self.shakeCamera(2)
				FlashWorldEnv(0.3,10)

			"rstsh":
				Profile.resetShips()

			"unlsh":
				if(Config.Env.IsDevelopment):
					unlockNextShip()
			"tspwu":

				var pwud = Global.doDropPowerup()
				if(pwud>=0):
					spawnPowerup( Vector2(Global.getWindowSize().x/2,-100) , int(pwud))

			"sppws":
				spawnPowerupShip()

			"spslc":
				spawnLargeCrystal(false)

			"spllc":
				spawnLargeCrystal(true)

			"gvcma":
				spawnPowerup( Vector2(Global.getWindowSize().x/2,-100) , Global.PowerupType.A)

			"gvcmb":
				spawnPowerup( Vector2(Global.getWindowSize().x/2,-100) , Global.PowerupType.B)

			"gvcmc":
				spawnPowerup( Vector2(Global.getWindowSize().x/2,-100) , Global.PowerupType.C)

			"blhll":
				spawnBulletHell()

			"kilal":
				if(Config.Env.IsDevelopment):
					levelConductor.levelInstance.killEverything()

			"lvnxt":
				if(Config.Env.IsDevelopment):
					levelConductor.levelInstance.killEverything()
					levelConductor._initLevelLoad()

			"idkfa":
				if player:
					PlayerBulletType = Global.PlayerBulletTypes.HOMING_TRIPPLE_SUPER
					PlayerSpeed = 400
					PlayerMaxBullets = Config.MaxBulletsLimit
					player.setAutoFire(true);

					playerEffets[Global.PlayerEffect.SUPER_RAPIDFIRE] = Config.TimedEffectBaseLong
					playerEffets[Global.PlayerEffect.CRYSTAL_EXPLOSION] = Config.TimedEffectBase

					for _i in range(Global.getMaxWingCount()):
						player.addWings(Global.PlayerBulletTypes.HOMING_SINGLE);
			"iddqd":
				if(player):
					player.toggleGodMode()

			"kilme":
				if(player):
					player.die()

			"effsr":
					playerEffets[Global.PlayerEffect.SUPER_RAPIDFIRE] = 500
			"effcx":
					playerEffets[Global.PlayerEffect.CRYSTAL_EXPLOSION] = 500
			"gvecr":
				spawnBonusCrystals(100,true)

			"freee":
				money += 10000

			"gveli":
				lives += 10

			"maxlk":
				PlayerLuck = 10.0

			"efflk":
				addPlayerEffect(Global.PlayerEffect.LUCK, 60)

			"bltop":
				isBulletOP=!isBulletOP

			"gvemi":
				getLevelObject().spawnSpaceMine(true);

			"gvdeb":
				for _i in range(1,10):
					spawnDebris(Global.DebrisType.Random)

			"gvemm":
				for _i in range(1,10):
					getLevelObject().spawnSpaceMine(fmod(_i,2)==0);

			"ikill":
				lives = 0
				player.die()

			"addtm":
				gameDuration =+ 300.0

			"ersac":
				Achievments.resetAll()

			"ersst":
				StoreIntegration.eraseStats()

			_:
				wasMatch = false
		
	if( (wasMatch || wasCustomMatch) && doCountAsCheat):

		if(!Config.Env.IsDevelopment):
			Global.setCheatState(true)

		# displayNotification("`" + cheat +"`", "Cheat activated: ")
		displayNotification(cheat,"Cheat")

		if cheat!="ersac":
			Achievments._add("cheater", "Cheater", "Cheater,  space-pumpkin eater!",false, true)
			Achievments.save()
			StoreIntegration.setAchievement("cheater")

		Global.playTts(SoundManager.tts_cheater_cheater)

func debrisDestroyed(_object):
	if(levelConductor.levelInstance.has_method("debrisDestroyed")):
		levelConductor.levelInstance.debrisDestroyed(_object)

func spawnDebris(type = Global.DebrisType.Random):

	var o = null

	var is_small = (type==Global.DebrisType.Small)

	if(type==Global.DebrisType.Random):
		is_small = randi()%10>5

	if(is_small):
		o = SmallDebris.instance()
	else:
		o = LargeDebris.instance()

	Global.GameScene.add_child(o);
	o.connect("debris_destroyed", self, "debrisDestroyed")

	Global.GameScene.addIndicator(o.global_position.x, Global.OISize.Small if is_small else Global.OISize.Medium ,o.getHealthLevelForIndicator(),"Junk")

	return o

func displayNotification(notification, prefix = "", bullet = "> ", duration = 3):
	Global.playSound(SoundManager.Notification2, Global.getWindowSize()/Vector2(2,2), 0)
	addTerminalLine(bullet + prefix.to_upper() + Global.ifelse(prefix=="","",": ") + notification.to_upper(), duration)

func isBossLevel():
	return levelConductor.getCurrent().isBossLevel();

func getPlayerLuck():
	if(hasPlayerEffect(Global.PlayerEffect.LUCK)):
		return max(Config.PlayerMaxLuck, PlayerLuck)
	
	# if player very accurate, then higher luck
	if(levelConductor.getAccuracy()>=Config.AccuracyBonusLimit && levelConductor.getCurrent().getLevelType()!=Global.LevelType.BOSS):
		return min(Config.PlayerMaxLuck, PlayerLuck+4.0)
	
	var _res = PlayerLuck*ShipSpecs.getSpecs().luck_multiplier

	# if easy mode, then add 20% to luck
	if(difficulty==Global.GameDifficulty.EASY):
		_res = _res*1.2
	
	# cap luck
	_res = min(Config.PlayerMaxLuck, _res)

	return _res

# some global stuff
var isCurrentBossReady = false
var didLevelStart = false

var wasDiscoBallDestoryed = Global.DiscoBallState.NIL

var extraLifeGiven = 0
var previousLimit = 0

func addScore(amount, scorePos = null):
	# calculate difficulty into point system
	var incAmount = (amount+(500*difficulty))
	score = score + incAmount

	if(score < 0):
		score = 0
	
	# add extra life if point reached
	var extraLifeLimit = previousLimit + (extraLifeGiven+1)*Config.ExtraLifeInterval
	
	if(extraLifeLimit<score && lives<=Config.ShipMaxLives):
		previousLimit = extraLifeLimit
		extraLifeGiven+=1
		lives+=1
		Global.playSound(SoundManager.ExtraLife, Global.getPlayerPosition(), -5)
		Achievments.acquire("extra_life")
		spawnBonusLabel(Global.getWindowSize()/Vector2(2.0,2.0), str(extraLifeLimit) + " Extra Life!",0.3, false, true, 1,5,0,true);
		displayNotification("Extra Life!",str(extraLifeLimit)+"pts")
		Global.playTts(SoundManager.tts_extra_life)
	else:
		if(scorePos && score>0):
			spawnBonusLabel(scorePos,"+"+str(incAmount),2,true,false,0.8,5,0,true);

func setDifficulty(diff):
	difficulty = diff

# proper difficulty counter - with each level offset we increase
func getProperDifficulty():
	return difficulty + levelConductor.playThroughCounter

func addPlayerEffect(key, amount, durationModifier = 0):

	if(durationModifier>0):
		amount = durationModifier

	self.playerEffets[key] += amount

func togglePause():
	get_node("Pause").togglePause()

func showQuitDialog():
	get_node("Pause/QuitDialog").popup()

func quitGame():
	get_node("Pause").pauseOff()
	backToTitle()

var levelData = null
var patternData = null

func loadData():

	if(isMode(Global.GameMode.CAMPAIGN)):
		levelData = LevelData.getCampaignData()
	elif(isMode(Global.GameMode.FLOW)):

		var randomLevelData = LevelData.generateShmupLevelData()

		# @todo genearte random level from X waves depending, and maybe final boss
		var _randomLevelData = {"data":[
			{
			"Title": "Kill 'em all!",
			"Type": "Bonus",
			"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
			"Music": "shmup1",
			"Config": [
				[1, 1, 1, 1, 1, 1, 1, 1, 1],
				[1, 1, 1, 1, 1, 1, 1, 1, 1],
				[1, 1, 1, 1, 1, 1, 1, 1, 1],
				[1, 1, 1, 1, 1, 1, 1, 1, 1],
				[1, 1, 1, 1, 1, 1, 1, 1, 1]
			],
			"EntryDelay": [],
			"EntryPattern": [
				{
				"type": "BonusPreset",
				"value": [0, 0, 0, 0, 0]
				},
				{
				"type": "BonusPreset",
				"value": [0, 1, 0, 0, 0]
				},
				{
				"type": "BonusPreset",
				"value": [1, 0, 0, 0, 0]
				},
				{
				"type": "BonusPreset",
				"value": [1, 1, 0, 0, 0]
				},
				{
				"type": "BonusPreset",
				"value": [2, 1, 0, 0, 0]
				}
			]
			},
	{
	  "Title": "Don't hurt us!",
	  "Type": "Normal",
	  "Music": "shmup1",
	  "Config": [
		[0, 0, 1, 1, 0, 1, 1, 0, 0],
		[0, 1, 1, 1, 1, 1, 1, 1, 0],
		[0, 0, 1, 1, 1, 1, 1, 0, 0],
		[0, 0, 0, 1, 1, 1, 0, 0, 0],
		[1, 0, 0, 0, 1, 0, 0, 0, 1]
	  ],
	  "WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	  "EntryDelay": [],
	  "EntryPattern": [
		{
		  "type": "BasicPreset",
		  "value": [0, 0, 0, 0, 0]
		},
		{
		  "type": "BasicPreset",
		  "value": [0, 1, 0, 0, 0]
		},
		{
		  "type": "BasicPreset",
		  "value": [1, 0, 0, 0, 0]
		},
		{
		  "type": "BasicPreset",
		  "value": [1, 1, 0, 0, 0]
		},
		{
		  "type": "BasicPreset",
		  "value": [2, 1, 0, 0, 0]
		}
	  ]
	},
	{
	  "Title": "With all you got!",
	  "Type": "Rush",
	  "Music": "shmup1",
	  "Repo": [1, 2]
	},
	{
	"Title": "Itsy Bitsy...",
	"Type": "Boss",
	"Music": "shmup1",
	"Scene": "Boss4"
	},
			]}


		levelData = randomLevelData

	patternData = PatternData.getData()

var disabledPowerups = []

func configParamsForMode():
	if(isMode(Global.GameMode.FLOW)):
		PlayerMaxBullets+=4
		PlayerSpeed += 3*Config.ShipSpeedIncrement
		setSpeed(true)
	else:
		PlayerMaxBullets+=2
		PlayerSpeed += 2*Config.ShipSpeedIncrement
		setSpeed(true)

var idle_time = 0.0
var hide_delay = 2.0  # change this to N seconds
var last_mouse_position = Vector2.ZERO

# function to hide / show mouse cursor when needed
func mouseCrsr(delta):

	var current_pos = get_viewport().get_mouse_position()

	if current_pos != last_mouse_position:
		idle_time = 0
		last_mouse_position = current_pos
		# Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
		Global.hideMouse(Global.isMouseControl())
	else:
		idle_time += delta
		if idle_time >= hide_delay:
			Input.set_mouse_mode(Input.MOUSE_MODE_HIDDEN)

func _ready():


	# demo label and timer
	# startTick = Tick.ms()
	# get_node("Overlay/DemoCountdown").visible = Config.Env.IsDemo;

	last_mouse_position = get_viewport().get_mouse_position()
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)

	State.clearAll()

	# hide controls on non touch screens
	if not OS.has_touchscreen_ui_hint():
		$"/root/Main/Controller".visible = false
		$"/root/Main/Game/PauseButton".visible = false
	
	# set powerup types for mouse game
	if(Global.OptionsData.controlType==Global.GameControlMode.MOUSE):
		disabledPowerups.push_back(Global.PowerupType.EXTRA_SPEED)
	
	# we'll give player autofire no matter what, it's a better feeling
	# if(isMode(Global.GameMode.FLOW)):
	#	disabledPowerups.push_back(Global.PowerupType.AUTOFIRE)

	disabledPowerups.push_back(Global.PowerupType.AUTOFIRE)

	# config params
	configParamsForMode()

	# seed random
	randomize()

	# set the global variable to this scene so other scenes can access it
	Global.GameScene = self;

	# Initialize bullet pools for optimized bullet management
	bullet_pool = BulletPool.create_bullet_pool(self)
	bullet_super_pool = BulletPool.create_bullet_super_pool(self)
	bullet_homing_pool = BulletPool.create_bullet_homing_pool(self)
	bullet_laser_pool = BulletPool.create_bullet_laser_pool(self)

	# read levels
	loadData()

	# hide mouse cursor

	Global.hideMouse(Global.isMouseControl())

	# set pause buttons
	get_node("Pause").pauseOff()
	get_node("PauseButton").connect("pressed", self, "togglePause")
	get_node("Pause/QuitButton").connect("pressed", self, "showQuitDialog")
	get_node("Pause/PauseButton").connect("pressed", self, "togglePause")
	get_node("Pause/QuitDialog").get_ok().connect("pressed", self, "quitGame")

	# set difficulty
	setDifficulty(Global.SelectedDifficulty)

	# start game
	levelConductor = get_node("LevelConductor")
	levelConductor.start()

	# achievements
	Achievments.acquire("first_game")

	# add wings
	Global.setTimeout(self,3,self,"addWingsFromProfile",[Profile.getStat("wing1",0),Profile.getStat("wing2",0),Profile.getStat("wing3",0)])

	# reset profile
	Profile.resetStats()

func addWingsFromProfile(wing1, wing2, wing3):

	for _i in range(wing1):
		player.addWings(Global.PlayerBulletTypes.SINGLE);

	for _i in range(wing2):
		player.addWings(Global.PlayerBulletTypes.STRONG_SINGLE);

	for _i in range(wing3):
		player.addWings(Global.PlayerBulletTypes.HOMING_SINGLE);


func getLevelObject():
	return levelConductor.getCurrent()

func getLevelNumber():
	return levelConductor.getLevelNumber()

func getLevelType():
	return levelConductor.getCurrent().getLevelType()

func backToTitle():
	Global.backToTitle()

func openStats():
	Global.lastStatsObject = self.levelConductor.getSummaryStats()
	var _s = get_tree().change_scene("res://scenes/StatsDisplay.tscn")

var didPlayerMoveAtAll = false;

func resetPlayer():
	# check if enemies are all idle
	if(levelConductor.getCurrent().canPlayerRespawn()):

		# easter eg if player did nothing
		if(!didPlayerMoveAtAll):
			self.PlayerBulletType = Global.PlayerBulletTypes.HOMING_TRIPPLE_SUPER
			Achievments.acquire("passive_agressive")

		addPlayer()

		if(!didPlayerMoveAtAll):
			player.addWings(Global.PlayerBulletTypes.SINGLE);
			didPlayerMoveAtAll = true
		
		player.giveTemporalInvincibility(Config.TimedEffectShieldRespawn)

		Global.playMusicInstantly($BackgroundMusicPlayer)
		pass
	else:
		Global.setTimeout(self,1,self,"resetPlayer");
		pass
	
var summaryStatsObject = {}

func saveDataToProfile():
	# calculate final high score from all the stuff, and add it

	Global.lastScoreObject = {
		"score": score,
		"mode": Global.GameScene.getModeStr(),
		"summaryStatsObject": summaryStatsObject,
		"difficulty": Global.GameScene.difficulty,
		"version": Config.version
	}

	# Profile.saveHighScore(score, Global.GameScene.getModeStr(), summaryStatsObject, Global.GameScene.difficulty, Config.version)

	if(!Global.getCheatState()):
		Profile.incSaveCrystal(money)

func finishGame():
	summaryStatsObject = levelConductor.getSummaryStats()
	saveDataToProfile()

func _on_Player_died():
	lives -= 1
	if(lives < 0):

		if !levelConductor.wasAnyKill:
			Achievments.acquire("no_kill_death")

		Global.stopMusicInstantly($BackgroundMusicPlayer)
		get_node("TVFrame").visible = true
		displayMessage("Game Over",2)
		Global.playTts(SoundManager.tts_game_over)

		finishGame()
		# levelConductor.getSummaryStatsText(true)

		Global.setTimeout(self,2.5,self,"openStats");
		pass
	else:
		displayMessage("Lives left: " + str(lives))
		Global.playTts(SoundManager.tts_get_ready)
		Global.setTimeout(self,2,self,"resetPlayer");
		pass

func wiggle(delta):
	wigglePosition += wiggleDirection*Config.WiggleMultiplier*delta

	if(wigglePosition > Config.WiggleMax):
		wigglePosition = Config.WiggleMax
		wiggleDirection = -1.0
	elif(wigglePosition < -Config.WiggleMax):
		wigglePosition = -Config.WiggleMax
		wiggleDirection = 1.0
	
func spawnManyCrystals(cnt, coord, doRandomize = false, doSpreadMore = false, typeArray = [
	Global.CrystalType.c5,
	Global.CrystalType.c10,
	Global.CrystalType.c20,
	Global.CrystalType.c50,
	Global.CrystalType.c100,
]):
	for _i in range(0,cnt):

		var _pos = coord

		if(doRandomize):
			_pos.x = 3*(int(Global.getWindowSize().x/8))+randi()%int(Global.getWindowSize().x/4)
			_pos.y = -(20+randi()%int(Global.getWindowSize().y*0.6))

		var _type = typeArray[0]

		var chance = [0,2,20,50,150,500,1000]

		for _ci in len(typeArray):
			if(chance[_ci]==0 || randi()%chance[_ci]==0):
				_type = typeArray[_ci]

		spawnCrystal(_pos, _type, doSpreadMore)

func add_child_deferred(child):
	call_deferred("add_child", child)

func spawnBonusCrystals(cnt, doRandomize = false, initialPosition = Vector2(0,0), doSpreadMore = false):

	var _pos_base = Global.getPlayer().position
	_pos_base.y = -20

	if(initialPosition!=Vector2(0,0)):
		_pos_base = initialPosition

	self.spawnManyCrystals(cnt,_pos_base,doRandomize,doSpreadMore,[
		Global.CrystalType.c5,
		Global.CrystalType.c10,
		Global.CrystalType.c20,
		Global.CrystalType.c50,
		Global.CrystalType.c100
	]);


func spawnCrystal(position,cType, doSpreadMore = false):
	var crystal = Crystal.instance()
	crystal.z_index = Config.TopZIndex-10
	crystal.position = position
	add_child_deferred(crystal)
	crystal.call_deferred("init",cType, doSpreadMore)

func spawnBonusLabel(position, text, animation_speed = 1.0, isTextOnly = false, isOminuous = false, textScale = 1.0, shake = 0, rotationSpeed = 0, doGrow = false):
	var bonusLabel = BonusMultiplierLabel.instance()
	bonusLabel.z_index = Config.TopZIndex+10
	bonusLabel.position = position
	add_child(bonusLabel)
	bonusLabel.addShake = shake
	bonusLabel.rotationSpeed = rotationSpeed
	bonusLabel.doGrow = doGrow
	bonusLabel.call_deferred("init",text, animation_speed, isTextOnly, isOminuous, textScale)

func spawnPowerup(position, type, speed = 0, duration = 0):

	# if the powerup is not allowed in the game, then remove it
	if(disabledPowerups.find(type)>=0):
		return false

	var powerup = Powerup.instance()
	powerup.z_index = Config.TopZIndex-10
	powerup.position = position
	powerup.durationModifier = duration
	powerup.speedModifier = speed
	add_child(powerup)
	powerup.call_deferred("init",type)

var terminalContent = []

func addTerminalLine(line,duration = 5):

	terminalContent.push_back([line,duration,Tick.ms()])

	if(terminalContent.size()>10):
		terminalContent.pop_front()

var gameDuration = 0.0

func addIndicator(xposition, size,type,text, lifeTime = 3):
	var _oi = ObjectIndicator.instance()
	_oi.global_position = Vector2(xposition, 50)
	add_child(_oi)
	_oi.init(size,type,text,lifeTime)

# var demoTimer = 6*60
# # var demoTimer = 20
# var startTick = 0
# var _msg1 = false
# var _msg2 = false

func updateUI(_delta):

	# coundtiwn demo timer
	# if Config.Env.IsDemo:
	# 	var currDemoTime = demoTimer - ((Tick.ms() - startTick) / 1000.0) # Use 1000.0 for float division

	# 	if(currDemoTime<=0):
	# 		currDemoTime = 0

	# 	var minutes = int(currDemoTime / 60)
	# 	var seconds = int(fmod(currDemoTime, 60)) # Use fmod for float modulo

	# 	if(currDemoTime<=10 and currDemoTime>5 && !_msg1):
	# 		displayMessage("THIS WAS THE DEMO!", 3, 1)

	# 		addTerminalLine("      #   #      ", 3)
	# 		addTerminalLine("     # # # #     ", 3)
	# 		addTerminalLine("     #  #  #     ", 3)
	# 		addTerminalLine("      #   #      ", 3)
	# 		addTerminalLine("       # #       ", 3)
	# 		addTerminalLine("        #        ", 3)
	# 		addTerminalLine("                 ", 3)
	# 		addTerminalLine("YOU'RE AWESOME :)", 3)

	# 		_msg1 = true

	# 	if(currDemoTime<=5 && !_msg2):
	# 		displayMessage("THANKS FOR PLAYING!", 5, 1)
	# 		finishGame()
	# 		Global.setTimeout(self,10,self,"openStats");

	# 		addTerminalLine("      *   *      ", 5)
	# 		addTerminalLine("     *** ***     ", 5)
	# 		addTerminalLine("     *******     ", 5)
	# 		addTerminalLine("      *****      ", 5)
	# 		addTerminalLine("       ***       ", 5)
	# 		addTerminalLine("        *        ", 5)
	# 		addTerminalLine("                 ", 5)
	# 		addTerminalLine(" -- THANK YOU -- ", 5)

	# 		_msg2 = true


	# 	if(currDemoTime<=5):

	# 		if(currDemoTime<=1):
	# 			get_node("Overlay/DemoCountdown").text = "WE HOPE YOU HAD A BLAST :) !"
	# 			displayMessage("BYE :) !", 2, 1)
	# 		else:
	# 			get_node("Overlay/DemoCountdown").text = "DEMO %02d:%02d" % [minutes, seconds]

	# 		get_node("Overlay/DemoCountdown").modulate.r = 2.0
	# 		get_node("Overlay/DemoCountdown").modulate.g = 0.5
	# 		get_node("Overlay/DemoCountdown").modulate.b = 0.5
	# 		get_node("Overlay/DemoCountdown").modulate.a = 2.0
	# 	else:
	# 		get_node("Overlay/DemoCountdown").text = "DEMO ENDS IN %02d:%02d" % [minutes, seconds]



	if Global.doThrottle("updateUI",500):
		pass
	
	var _temp = []

	# remove expired
	for i in range(terminalContent.size()):
		var _l = terminalContent[i]
		if((_l[1]*1000+_l[2])>Tick.ms()):
			_temp.push_back(_l)
	
	terminalContent = _temp
	
	get_node("Overlay/LifeLabel").text = "Lives:\n" + str(max(lives,0))
	
	get_node("Overlay/VoidScore").visible = Global.getCheatState()
	get_node("Overlay/VoidMoney").visible = Global.getCheatState()

	var postStr = (" :)" if levelConductor.getLevelNumber()==69 else "")

	if(isMode(Global.GameMode.CAMPAIGN)):
		get_node("Overlay/LevelLabel").text = "Level:\n" + str(levelConductor.getLevelNumber()) + postStr
	elif(isMode(Global.GameMode.FLOW)):
		get_node("Overlay/LevelLabel").text = "Flow:\n" + str(levelConductor.getLevelNumber()) + postStr

	get_node("Overlay/ScoreLabel").text = str(score)
	get_node("Overlay/MoneyLabel").text = "$" + str(money)

	get_node("Overlay/TerminalLabel").text = ""
	var _lcnt = 0

	var _crsr = (int((gameDuration/0.5))%2 == 0)

	for _tl in terminalContent:
		var _tline = _tl[0]
		get_node("Overlay/TerminalLabel").text += _tline
		_lcnt+=1
		if(_lcnt==terminalContent.size()):
			get_node("Overlay/TerminalLabel").text += Global.ifelse(_crsr,".","_")
		else:
			get_node("Overlay/TerminalLabel").text +=  " \n"

	# set effects

	var effectsText = ""

	#stats

	effectsText += "------\n"

	var _st = max(0,Global.getSpeedIndex(PlayerSpeed))

	if(Global.OptionsData.controlType!=Global.GameControlMode.MOUSE):
		effectsText += "S:" + str(_st) + "\n"
	
	var _bt = max(0,Global.getBulletIndex(getPlayerMaxBullets()))

	effectsText += "B:" + str(_bt) + "\n"
	effectsText += "W:" + Global.getBulletData(PlayerBulletType)["char"] + "\n"
	# overheating in %
	# effectsText += "H:" + str(bulletsOnScreenPercentage()) + "%\n"

	#curses

	if(_secretModuleContainer!=""):
		effectsText += "/"+_secretModuleContainer+"/\n"

	#extras

	if(shieldCount>0):
		effectsText += "SH:" + str(shieldCount) + "\n"

	# if(PlayerAutoFire || isMode(Global.GameMode.FLOW)):
	# 	effectsText += "A:ON\n"

	# effectsText += "------\n"
	# effectsText += "AC:\n" + str(levelConductor.getAccuracy()) + "%\n"

	effectsText += "------\n"

	#effects

	var _positiveEffect = ""

	if(hasPlayerEffect(Global.PlayerEffect.SUPER_RAPIDFIRE)):
		_positiveEffect += "SR " + str(playerEffets[Global.PlayerEffect.SUPER_RAPIDFIRE]) +"\n"

	if(hasPlayerEffect(Global.PlayerEffect.INVINCIBILITY)):
		_positiveEffect += "IV " + str(playerEffets[Global.PlayerEffect.INVINCIBILITY]) +"\n"

	if(Global.getPlayer()):
		Global.getPlayer().displayInvincibility()

	if(hasPlayerEffect(Global.PlayerEffect.CRYSTAL_EXPLOSION)):
		_positiveEffect += "CX " + str(playerEffets[Global.PlayerEffect.CRYSTAL_EXPLOSION]) +"\n"

	if(hasPlayerEffect(Global.PlayerEffect.LUCK)):
		_positiveEffect += "LK " + str(playerEffets[Global.PlayerEffect.LUCK]) +"\n"
	
	if(hasPlayerEffect(Global.PlayerEffect.CRYSTAL_MAGNET)):
		_positiveEffect += "CM " + str(playerEffets[Global.PlayerEffect.CRYSTAL_MAGNET]) +"\n"

	if(_positiveEffect!=""):
		effectsText+=_positiveEffect
		effectsText+="------\n"
	
	var _negativeEffects = ""

	if(hasPlayerEffect(Global.PlayerEffect.WEAPON_DISABLED)):
		_negativeEffects += "WD " + str(playerEffets[Global.PlayerEffect.WEAPON_DISABLED]) +"\n"

	if(hasPlayerEffect(Global.PlayerEffect.SLOTH_MODE)):
		_negativeEffects += "SM " + str(playerEffets[Global.PlayerEffect.SLOTH_MODE]) +"\n"

	if(_negativeEffects!=""):
		effectsText+=_negativeEffects
		effectsText+="------\n"

	get_node("Overlay/EffectsLabel").text = effectsText

func getGameDuration():
	return int(gameDuration)

func debug():
	if Global.doThrottle("debug_x",500):
		return false
	
	# add debug print here

	pass

func hasPlayerEffect(key):
	return playerEffets[key]>0

func removeAllEffects():
	for _eff in playerEffets:
		playerEffets[_eff] = 0

func decreasePlayerEffects():

	for i in playerEffets:
		if(playerEffets[i]>0):
			playerEffets[i]-=1
	
func _process(delta):

	mouseCrsr(delta)

	gameDuration += delta

	if(!Global.doThrottle("decrease_player_effect", 1000*ShipSpecs.getSpecs().timed_powerup_duration_multiplier)):
		if self.levelConductor.didLevelStart():
			self.decreasePlayerEffects()

	debug()

	wiggle(delta)

	updateUI(delta)

	#debug

	if(Input.is_action_just_pressed("button_debug")):
		checkCheat(self._type_storage)

	pass


var PlayerScene = preload("res://scenes/Player.tscn")

var player = null

func isPlayerReady() -> bool:
	if player == null:
		return false
	else:
		return player.mode == Global.PlayerMode.NORMAL
	
func setInitialSpecs():

	# give wings from specs by timeout
	for _i in ShipSpecs.getSpecs().initial_wing_count:
		player.addWings(ShipSpecs.getSpecs().initial_wing_bullet_type);
	
	# set start weapon ( if smaller then purchased )
	if(PlayerBulletType<ShipSpecs.getSpecs().initial_weapon_type):
		PlayerBulletType = ShipSpecs.getSpecs().initial_weapon_type


	pass


func addPlayer(isInitial = false):
	player = PlayerScene.instance()
	player.z_index = Config.TopZIndex+100
	player.connect("player_death", self, "_on_Player_died")
	add_child(player)
	if(isInitial):
		setInitialSpecs()
	
func displayMessage(text, delay = 1.5, alpha = 1):

	var _label = get_node("LevelTitle/MessageLabel");

	_label.set_text(text)
	_label.visible = true
	_label.modulate.a = alpha

	Global.setTimeout(self,delay,self,"hideLabel");

func hideLabel():
	get_node("LevelTitle/MessageLabel").visible = false

func addBullets(amount):
	BulletsOnScreen += amount

func playBulletSound1():
	Global.playSound(SoundManager.Bullet1Sound, Global.getPlayerPosition(), -5)

func playBulletSound2():
	Global.playSound(SoundManager.BulletLaser, Global.getPlayerPosition(), -10)

signal bullet_fired(bullet_type)

func _spawnBulletForBulletHell(_i, _max):

	playBulletSound1();

	var homingBullet = bullet_homing_pool.get_bullet()
	homingBullet.position = Vector2( _i * (Global.getWindowSize().x / _max),  Global.getWindowSize().y )
	homingBullet.modulate.g = 3
	homingBullet.scale.x = 1.2
	homingBullet.scale.y = 1.2

	var homingBullet2 = bullet_homing_pool.get_bullet()
	homingBullet2.position = Vector2( Global.getWindowSize().x - (_i * (Global.getWindowSize().x / _max)),  Global.getWindowSize().y )
	homingBullet2.modulate.g = 3
	homingBullet2.scale.x = 1.2
	homingBullet2.scale.y = 1.2

func spawnBulletHell():
	var bulletCnt = 30
	for _i in range(1,bulletCnt):
		Global.setTimeout(self,0.07*_i,self,"_spawnBulletForBulletHell",[_i, bulletCnt])

func bulletsOnScreenPercentage():
	# super rapid fire, user has 100% of bullets
	if hasPlayerEffect(Global.PlayerEffect.SUPER_RAPIDFIRE):
		return 0
	
	var percentage = int((float(BulletsOnScreen)/float(getPlayerMaxBullets()))*100)

	return max(0,min(100,percentage))

func spawnBullets(bulletType, startPosition):

	var bullet_offset = 34;

	# if we have enough bullets, then don't spawn any more
	if ((BulletsOnScreen >= getPlayerMaxBullets()) and !hasPlayerEffect(Global.PlayerEffect.SUPER_RAPIDFIRE)):
		return false
	
	bulletType = int(bulletType)

	emit_signal("bullet_fired", bulletType)

	match bulletType:

		Global.PlayerBulletTypes.SINGLE:

			playBulletSound1();

			var bullet = bullet_pool.get_bullet()
			bullet.position = startPosition - Vector2(0, bullet_offset)

			bullet.canClash = true

			addBullets(1)

		Global.PlayerBulletTypes.DOUBLE:

			playBulletSound1();

			var bullet1 = bullet_pool.get_bullet()
			bullet1.position = startPosition - Vector2(-8, bullet_offset)

			var bullet2 = bullet_pool.get_bullet()
			bullet2.doCountTowardBulletsOnScreen = false
			bullet2.position = startPosition - Vector2(8, bullet_offset)

			addBullets(1)

		Global.PlayerBulletTypes.TRIPPLE:

			playBulletSound1();

			var bullet1 = bullet_pool.get_bullet()
			bullet1.position = startPosition - Vector2(-8, bullet_offset)

			var bullet2 = bullet_pool.get_bullet()
			bullet2.doCountTowardBulletsOnScreen = false
			bullet2.position = startPosition - Vector2(0, bullet_offset)

			var bullet3 = bullet_pool.get_bullet()
			bullet3.doCountTowardBulletsOnScreen = false
			bullet3.position = startPosition - Vector2(8, bullet_offset)

			addBullets(1)

			bullet1.setXVelocity(0.08)
			bullet3.setXVelocity(-0.08)

		Global.PlayerBulletTypes.STRONG_SINGLE:

			playBulletSound1();

			var bullet2 = bullet_super_pool.get_bullet()
			bullet2.position = startPosition - Vector2(0, bullet_offset)

			addBullets(1)

		Global.PlayerBulletTypes.STRONG_TRIPPLE:

			playBulletSound1();

			var bullet1 = bullet_pool.get_bullet()
			bullet1.position = startPosition - Vector2(-8, bullet_offset)

			var bullet2 = bullet_super_pool.get_bullet()
			bullet2.doCountTowardBulletsOnScreen = false
			bullet2.position = startPosition - Vector2(0, bullet_offset)

			var bullet3 = bullet_pool.get_bullet()
			bullet3.doCountTowardBulletsOnScreen = false
			bullet3.position = startPosition - Vector2(8, bullet_offset)

			addBullets(1)

			bullet1.setXVelocity(0.1)
			bullet3.setXVelocity(-0.1)

		Global.PlayerBulletTypes.HOMING_SINGLE:

			playBulletSound1();

			var homingBullet = bullet_homing_pool.get_bullet()
			homingBullet.position = startPosition - Vector2(0, bullet_offset)

			addBullets(1)

		Global.PlayerBulletTypes.HOMING_TRIPPLE:

			playBulletSound1();

			var bullet = bullet_pool.get_bullet()
			bullet.position = startPosition - Vector2(0, bullet_offset)

			var homingBullet2 = bullet_homing_pool.get_bullet()
			var homingBullet3 = bullet_homing_pool.get_bullet()

			homingBullet2.position = startPosition - Vector2(-20, bullet_offset-15)
			homingBullet2.doCountTowardBulletsOnScreen = false
			homingBullet3.position = startPosition - Vector2(+20, bullet_offset-15)
			homingBullet3.doCountTowardBulletsOnScreen = false

			addBullets(1)

		Global.PlayerBulletTypes.HOMING_TRIPPLE_SUPER:

			playBulletSound1();

			var bullet = bullet_super_pool.get_bullet()
			bullet.position = startPosition - Vector2(0, bullet_offset)

			var homingBullet2 = bullet_homing_pool.get_bullet()
			var homingBullet3 = bullet_homing_pool.get_bullet()

			homingBullet2.position = startPosition - Vector2(-20, bullet_offset-15)
			homingBullet2.doCountTowardBulletsOnScreen = false
			homingBullet3.position = startPosition - Vector2(+20, bullet_offset-15)
			homingBullet3.doCountTowardBulletsOnScreen = false

			addBullets(1)

		Global.PlayerBulletTypes.LASER_SINGLE:

			playBulletSound2();

			var bullet2 = bullet_laser_pool.get_bullet()
			bullet2.position = startPosition - Vector2(0, bullet_offset)

			addBullets(1)

func handleLoot(position, doSkip = false):

	if(doSkip):
		return false

	# should enemy drop powerup?
	var eventualPowerup = Global.doDropPowerup()
	var eventualCrystal = Global.doDropCrystal()

	# throw crystals if power active	
	if(hasPlayerEffect(Global.PlayerEffect.CRYSTAL_EXPLOSION)):
		spawnManyCrystals(5,position,false,true,[Global.CrystalType.c5, Global.CrystalType.c10])

	if eventualPowerup>=0:
		self.spawnPowerup(position, eventualPowerup)
	else:
		if eventualCrystal>=0:
			self.spawnCrystal(position, eventualCrystal)

func WorldEnvironment():
	return get_node("WorldEnvironment").environment;

func FlashWorldEnv(duration=0.2, intensity=2):
	var tween = Global.createTween(self)
	tween.interpolate_property(WorldEnvironment(),"adjustment_brightness",1,intensity,duration/2,Tween.TRANS_LINEAR, Tween.EASE_OUT);
	tween.start()

	var tween2 = Global.createTween(self)
	tween2.interpolate_property(WorldEnvironment(),"adjustment_brightness",intensity,1,duration/2,Tween.TRANS_LINEAR, Tween.EASE_OUT,duration/2);
	tween2.start()

	var tween3 = Global.createTween(self)
	tween3.interpolate_property(WorldEnvironment(),"adjustment_saturation",1,intensity,duration/2,Tween.TRANS_LINEAR, Tween.EASE_OUT);
	tween3.start()

	var tween4 = Global.createTween(self)
	tween4.interpolate_property(WorldEnvironment(),"adjustment_saturation",intensity,1,duration/2,Tween.TRANS_LINEAR, Tween.EASE_OUT,duration/2);
	tween4.start()

func getCamera():
	return get_node("Camera2D");

func shakeCamera(trauma=0.1):
	self.getCamera().add_trauma(trauma)

	
